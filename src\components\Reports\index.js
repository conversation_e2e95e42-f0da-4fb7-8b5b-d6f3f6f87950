'use client';
import React, { useContext, useLayoutEffect, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomTabs from '../UI/CustomTabs';
import { reportsMenuList } from '@/helper/common/commonMenus';
import AuthContext from '@/helper/authcontext';

const Reports = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);
  const [activeItemSlug, setActiveItemSlug] = useState('logbook_reports');
  const queryParams = new URLSearchParams(searchParams);
  const isReport = searchParams.get('is_report');
  const isActiveTab = searchParams.get('is_tab');
  const { authState } = useContext(AuthContext);

  useLayoutEffect(() => {
    if (authState && authState?.UserPermission) {
      const isPermission = reportsMenuList?.find(
        (item) =>
          item?.permission &&
          (authState?.UserPermission?.[item?.permission] === 2 ||
            authState?.UserPermission?.[item?.permission] === 1)
      );
      const isCheckPermission = reportsMenuList?.find(
        (item) => item?.id === Number(isReport)
      );
      if (
        isCheckPermission &&
        authState?.UserPermission?.[isCheckPermission?.permission] !== 2 &&
        authState?.UserPermission?.[isCheckPermission?.permission] !== 1
      ) {
        router.push(`/reports?is_report=${isPermission?.id}&is_tab=1`);
        setActiveMenuItem(Number(isPermission?.id));
        setActiveItemSlug(isPermission?.slug);
      } else {
        const isActiveSlug = reportsMenuList?.find(
          (item) => item?.id === Number(isReport)
        );
        setActiveMenuItem(Number(isReport || 1));
        setActiveItemSlug(isActiveSlug?.slug || 'logbook_reports');
      }
    }
  }, [authState, isReport]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    setActiveTab(tabIndex);
  }, [isActiveTab]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveItemSlug(item?.slug);
    setActiveTab(1);
    router.push(`/reports?is_report=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (val) => {
    setActiveTab(val);
    queryParams.set('is_tab', val);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.slug === activeItemSlug
    );
    return currentItem?.tabs || [];
  };

  const getCurrentContent = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.slug === activeItemSlug
    );
    return currentItem?.component;
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <CustomTabs
            tabs={getCurrentTabs()}
            initialTab={activeTab}
            onTabChange={handleTabChange}
            isSetup
          />
        </Box>
        <Box className="section-right-content">{getCurrentContent()}</Box>
      </Box>
    </Box>
  );
};

export default Reports;
