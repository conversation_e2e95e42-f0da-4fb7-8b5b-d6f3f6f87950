'use client';
import React, { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';

const Reports = () => {
  const [activeMenuItem, setActiveMenuItem] = useState(1);

  // Reports menu list
  const reportsMenuList = [
    {
      id: 1,
      name: 'Rota',
      slug: 'rota',
      icon: null,
    },
    {
      id: 2,
      name: 'Document',
      slug: 'document',
      icon: null,
    },
    {
      id: 3,
      name: 'Activity',
      slug: 'activity',
      icon: null,
    },
    {
      id: 4,
      name: 'Change Request',
      slug: 'change_request',
      icon: null,
    },
    {
      id: 5,
      name: 'New Employee Joining',
      slug: 'new_employee_joining',
      icon: null,
    },
    {
      id: 6,
      name: 'Staff User',
      slug: 'staff_user',
      icon: null,
    },
    {
      id: 7,
      name: 'Log Book',
      slug: 'log_book',
      icon: null,
    },
    {
      id: 8,
      name: 'Leave Balance Report per User',
      slug: 'leave_balance_per_user',
      icon: null,
    },
    {
      id: 9,
      name: 'Leave Consumption per Leave',
      slug: 'leave_consumption_per_leave',
      icon: null,
    },
    {
      id: 10,
      name: 'Recipe: Recipe CTA Analysis',
      slug: 'recipe_cta_analysis',
      icon: null,
    },
    {
      id: 11,
      name: 'Contact Submission',
      slug: 'contact_submission',
      icon: null,
    },
  ];

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        <Box className="section-right-content">
          {/* Your existing reports content will go here */}
        </Box>
      </Box>
    </Box>
  );
};

export default Reports;
