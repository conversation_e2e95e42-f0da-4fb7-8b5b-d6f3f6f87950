'use client';
import React, { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import DSRView from '@/components/DSR/Reports';

const Reports = () => {
  const [activeMenuItem, setActiveMenuItem] = useState(1);

  // Simple reports menu list
  const reportsMenuList = [
    {
      id: 1,
      name: 'Logbook Reports',
      slug: 'logbook_reports',
      icon: null,
    },
    {
      id: 2,
      name: 'Leave Reports',
      slug: 'leave_reports',
      icon: null,
    },
  ];

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
  };

  const getCurrentContent = () => {
    // For now, just show DSR Reports component
    return <DSRView />;
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        <Box className="section-right-content">{getCurrentContent()}</Box>
      </Box>
    </Box>
  );
};

export default Reports;
