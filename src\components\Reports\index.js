'use client';
import React, { useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import { reportsMenuList } from '@/helper/common/commonMenus';

const Reports = () => {
  const [activeMenuItem, setActiveMenuItem] = useState(1);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        <Box className="section-right-content">
          {/* Your existing reports content will go here */}
        </Box>
      </Box>
    </Box>
  );
};

export default Reports;
